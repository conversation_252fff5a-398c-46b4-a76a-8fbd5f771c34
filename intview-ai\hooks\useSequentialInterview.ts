'use client';

import { useState, useCallback, useEffect } from 'react';
import { 
  InterviewQuestion, 
  QuestionResponse, 
  InterviewSession, 
  SequentialInterviewState, 
  SequentialInterviewActions,
  DEFAULT_INTERVIEW_QUESTIONS,
  DIDConfig,
  InterviewError,
  DIDIntegrationError
} from '@/types/interview';
import { createDIDApiService } from '@/lib/did-api';

interface UseSequentialInterviewOptions {
  didConfig?: DIDConfig;
  questions?: InterviewQuestion[];
  autoGenerateVideos?: boolean;
}

export function useSequentialInterview(options: UseSequentialInterviewOptions = {}) {
  const {
    didConfig,
    questions = DEFAULT_INTERVIEW_QUESTIONS,
    autoGenerateVideos = true
  } = options;

  // State
  const [state, setState] = useState<SequentialInterviewState>({
    session: null,
    currentQuestion: null,
    currentVideoUrl: null,
    isGeneratingVideo: false,
    isSubmitting: false,
    userAnswer: '',
    error: null,
  });

  // D-ID service instance
  const didService = didConfig ? createDIDApiService(didConfig.apiKey) : null;

  // Helper function to update state
  const updateState = useCallback((updates: Partial<SequentialInterviewState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Generate video for a question
  const generateQuestionVideo = useCallback(async (question: InterviewQuestion): Promise<string | null> => {
    if (!didService || !didConfig) {
      console.warn('D-ID service not configured, skipping video generation');
      return null;
    }

    try {
      updateState({ isGeneratingVideo: true, error: null });
      
      const videoUrl = await didService.generateQuestionVideo(question.text, {
        sourceUrl: didConfig.sourceUrl,
        voiceId: didConfig.voiceId,
        provider: didConfig.provider,
      });

      return videoUrl;
    } catch (error) {
      console.error('Error generating question video:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate video';
      updateState({ error: `Video generation failed: ${errorMessage}` });
      return null;
    } finally {
      updateState({ isGeneratingVideo: false });
    }
  }, [didService, didConfig, updateState]);

  // Start the interview
  const startInterview = useCallback(async (customQuestions?: InterviewQuestion[]) => {
    try {
      const interviewQuestions = customQuestions || questions;
      const session: InterviewSession = {
        id: `interview_${Date.now()}`,
        startTime: new Date(),
        currentQuestionIndex: 0,
        questions: interviewQuestions,
        responses: [],
        status: 'in_progress',
      };

      const firstQuestion = interviewQuestions[0];
      let videoUrl: string | null = null;

      // Generate video for first question if auto-generation is enabled
      if (autoGenerateVideos && firstQuestion) {
        videoUrl = await generateQuestionVideo(firstQuestion);
      }

      updateState({
        session,
        currentQuestion: firstQuestion,
        currentVideoUrl: videoUrl,
        userAnswer: '',
        error: null,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start interview';
      updateState({ error: `Failed to start interview: ${errorMessage}` });
    }
  }, [questions, autoGenerateVideos, generateQuestionVideo, updateState]);

  // Submit answer and move to next question
  const submitAnswer = useCallback(async (answer: string) => {
    if (!state.session || !state.currentQuestion) {
      throw new InterviewError('No active interview session');
    }

    if (!answer.trim()) {
      updateState({ error: 'Please provide an answer before submitting' });
      return;
    }

    try {
      updateState({ isSubmitting: true, error: null });

      // Create response record
      const response: QuestionResponse = {
        questionId: state.currentQuestion.id,
        userAnswer: answer.trim(),
        timestamp: new Date(),
        videoUrl: state.currentVideoUrl || undefined,
      };

      // Update session with the response
      const updatedSession: InterviewSession = {
        ...state.session,
        responses: [...state.session.responses, response],
      };

      updateState({
        session: updatedSession,
        userAnswer: '',
      });

      // Move to next question
      await nextQuestion();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit answer';
      updateState({ error: `Failed to submit answer: ${errorMessage}` });
    } finally {
      updateState({ isSubmitting: false });
    }
  }, [state.session, state.currentQuestion, state.currentVideoUrl, updateState]);

  // Move to next question
  const nextQuestion = useCallback(async () => {
    if (!state.session) {
      throw new InterviewError('No active interview session');
    }

    const nextIndex = state.session.currentQuestionIndex + 1;
    
    if (nextIndex >= state.session.questions.length) {
      // Interview completed
      const completedSession: InterviewSession = {
        ...state.session,
        currentQuestionIndex: nextIndex,
        status: 'completed',
        endTime: new Date(),
      };

      updateState({
        session: completedSession,
        currentQuestion: null,
        currentVideoUrl: null,
      });
      return;
    }

    // Move to next question
    const nextQuestionObj = state.session.questions[nextIndex];
    let videoUrl: string | null = null;

    // Generate video for next question if auto-generation is enabled
    if (autoGenerateVideos && nextQuestionObj) {
      videoUrl = await generateQuestionVideo(nextQuestionObj);
    }

    const updatedSession: InterviewSession = {
      ...state.session,
      currentQuestionIndex: nextIndex,
    };

    updateState({
      session: updatedSession,
      currentQuestion: nextQuestionObj,
      currentVideoUrl: videoUrl,
      userAnswer: '',
    });
  }, [state.session, autoGenerateVideos, generateQuestionVideo, updateState]);

  // Other actions
  const pauseInterview = useCallback(() => {
    if (state.session) {
      updateState({
        session: { ...state.session, status: 'paused' }
      });
    }
  }, [state.session, updateState]);

  const resumeInterview = useCallback(() => {
    if (state.session) {
      updateState({
        session: { ...state.session, status: 'in_progress' }
      });
    }
  }, [state.session, updateState]);

  const completeInterview = useCallback(() => {
    if (state.session) {
      updateState({
        session: { 
          ...state.session, 
          status: 'completed',
          endTime: new Date()
        }
      });
    }
  }, [state.session, updateState]);

  const resetInterview = useCallback(() => {
    updateState({
      session: null,
      currentQuestion: null,
      currentVideoUrl: null,
      isGeneratingVideo: false,
      isSubmitting: false,
      userAnswer: '',
      error: null,
    });
  }, [updateState]);

  const setUserAnswer = useCallback((answer: string) => {
    updateState({ userAnswer: answer });
  }, [updateState]);

  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  // Actions object
  const actions: SequentialInterviewActions = {
    startInterview,
    submitAnswer,
    nextQuestion,
    pauseInterview,
    resumeInterview,
    completeInterview,
    resetInterview,
    setUserAnswer,
    clearError,
  };

  // Computed values
  const isInterviewActive = state.session?.status === 'in_progress';
  const isInterviewCompleted = state.session?.status === 'completed';
  const currentQuestionNumber = state.session ? state.session.currentQuestionIndex + 1 : 0;
  const totalQuestions = state.session?.questions.length || 0;
  const progress = totalQuestions > 0 ? (currentQuestionNumber / totalQuestions) * 100 : 0;

  return {
    // State
    ...state,
    
    // Actions
    ...actions,
    
    // Computed values
    isInterviewActive,
    isInterviewCompleted,
    currentQuestionNumber,
    totalQuestions,
    progress,
    
    // Utilities
    canSubmit: !state.isSubmitting && !state.isGeneratingVideo && state.userAnswer.trim().length > 0,
    hasError: !!state.error,
  };
}
