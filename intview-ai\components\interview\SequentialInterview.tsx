'use client';

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowR<PERSON>, Loader2, AlertCircle, Play, Pause } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useSequentialInterview } from '@/hooks/useSequentialInterview';
import { DIDConfig } from '@/types/interview';
import JobInfoCard from '@/components/JobInfoCard';
import InterviewLayout from '@/components/InterviewLayout';

interface SequentialInterviewProps {
  onComplete?: () => void;
  didConfig?: DIDConfig;
  className?: string;
}

const SequentialInterview: React.FC<SequentialInterviewProps> = ({
  onComplete,
  didConfig,
  className = '',
}) => {
  const [textareaValue, setTextareaValue] = useState('');
  
  const {
    session,
    currentQuestion,
    currentVideoUrl,
    isGeneratingVideo,
    isSubmitting,
    userAnswer,
    error,
    isInterviewActive,
    isInterviewCompleted,
    currentQuestionNumber,
    totalQuestions,
    progress,
    canSubmit,
    hasError,
    startInterview,
    submitAnswer,
    setUserAnswer,
    clearError,
  } = useSequentialInterview({
    didConfig,
    autoGenerateVideos: !!didConfig,
  });

  // Start interview on component mount
  useEffect(() => {
    if (!session) {
      startInterview();
    }
  }, [session, startInterview]);

  // Handle interview completion
  useEffect(() => {
    if (isInterviewCompleted && onComplete) {
      onComplete();
    }
  }, [isInterviewCompleted, onComplete]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (canSubmit && textareaValue.trim()) {
      await submitAnswer(textareaValue.trim());
      setTextareaValue('');
    }
  };

  // Handle textarea change
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setTextareaValue(value);
    setUserAnswer(value);
  };

  // Handle key down for textarea
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey && canSubmit) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };

  if (!session) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Initializing interview...</span>
        </div>
      </div>
    );
  }

  if (isInterviewCompleted) {
    return (
      <div className="h-screen">
        <JobInfoCard />
        <InterviewLayout>
          <div className="flex flex-col items-center justify-center space-y-6">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="text-center"
            >
              <h2 className="text-3xl font-bold text-green-600 mb-4">
                Interview Completed!
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                Thank you for completing the interview. Your responses have been recorded.
              </p>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-800">
                  You answered {session.responses.length} out of {totalQuestions} questions.
                </p>
              </div>
            </motion.div>
          </div>
        </InterviewLayout>
      </div>
    );
  }

  return (
    <div className={`h-screen ${className}`}>
      <JobInfoCard />
      
      <InterviewLayout>
        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-600">
              Question {currentQuestionNumber} of {totalQuestions}
            </span>
            <span className="text-sm font-medium text-gray-600">
              {Math.round(progress)}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className="bg-[#6938EF] h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start">
          {/* Left Side - Question and Input */}
          <div className="w-full lg:w-1/2 max-w-md space-y-6">
            {/* Current Question */}
            <AnimatePresence mode="wait">
              {currentQuestion && (
                <motion.div
                  key={currentQuestion.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  className="bg-white rounded-2xl p-6 shadow-sm border"
                >
                  <h3 className="text-lg font-semibold text-[#6938EF] mb-4">
                    Question {currentQuestionNumber}
                  </h3>
                  <p className="text-gray-800 text-lg leading-relaxed">
                    {currentQuestion.text}
                  </p>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Answer Input */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-2xl p-6 shadow-sm border"
            >
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="answer" className="block text-sm font-medium text-gray-700 mb-2">
                    Your Answer
                  </label>
                  <textarea
                    id="answer"
                    value={textareaValue}
                    onChange={handleTextareaChange}
                    onKeyDown={handleKeyDown}
                    placeholder="Type your answer here..."
                    className="w-full border-2 border-gray-200 rounded-xl px-4 py-3 resize-none focus:border-[#6938EF] focus:ring-2 focus:ring-[#6938EF]/20 transition-all duration-200 placeholder-gray-400"
                    rows={4}
                    disabled={isSubmitting || isGeneratingVideo}
                  />
                </div>

                {/* Error Display */}
                <AnimatePresence>
                  {hasError && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="flex items-center space-x-2 text-red-600 bg-red-50 border border-red-200 rounded-lg p-3"
                    >
                      <AlertCircle className="w-4 h-4 flex-shrink-0" />
                      <span className="text-sm">{error}</span>
                      <button
                        type="button"
                        onClick={clearError}
                        className="ml-auto text-red-400 hover:text-red-600"
                      >
                        ×
                      </button>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={!canSubmit}
                  className="w-full py-3 text-lg rounded-xl bg-[#6938EF] hover:bg-[#5a2fd8] disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200"
                >
                  {isSubmitting ? (
                    <div className="flex items-center space-x-2">
                      <Loader2 className="w-5 h-5 animate-spin" />
                      <span>Submitting...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <span>Submit Answer</span>
                      <ArrowRight className="w-5 h-5" />
                    </div>
                  )}
                </Button>
              </form>
            </motion.div>
          </div>

          {/* Right Side - Avatar Video */}
          <div className="w-full lg:w-1/2 max-w-md">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
              className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-6 shadow-sm border aspect-video relative overflow-hidden"
            >
              {isGeneratingVideo ? (
                <div className="flex flex-col items-center justify-center h-full space-y-4">
                  <Loader2 className="w-12 h-12 animate-spin text-[#6938EF]" />
                  <p className="text-gray-600 text-center">
                    Generating AI avatar video...
                  </p>
                </div>
              ) : currentVideoUrl ? (
                <video
                  src={currentVideoUrl}
                  controls
                  autoPlay
                  className="w-full h-full object-cover rounded-lg"
                  onError={() => console.error('Error loading video')}
                />
              ) : (
                <div className="flex flex-col items-center justify-center h-full space-y-4">
                  <div className="w-20 h-20 bg-gradient-to-br from-[#6938EF] to-[#8b5cf6] rounded-full flex items-center justify-center">
                    <Play className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">
                      AI Interviewer
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {didConfig ? 'Video will appear here' : 'Text-based interview mode'}
                    </p>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </InterviewLayout>
    </div>
  );
};

export default SequentialInterview;
