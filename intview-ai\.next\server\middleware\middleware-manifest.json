{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_5399b416._.js", "server/edge/chunks/node_modules_@auth_core_5ebafa38._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_e184ff1b._.js", "server/edge/chunks/[root-of-the-server]__df53d061._.js", "server/edge/chunks/edge-wrapper_3d09a47d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "kx5fv6OaRf1Y5ArHnAo9aEvVgDWGx4JcYen3hw8UgIA=", "__NEXT_PREVIEW_MODE_ID": "5c9b76608297cbf02f9d71db0b34a236", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "054cad18ac080ac05a0ad34e58560a348eee9067946dc61d280f50cf9594cf59", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ff0fa269d3d117161189f8abcf35c79de2194e31bb47b0c359d8b671a737f7bf"}}}, "instrumentation": null, "functions": {}}