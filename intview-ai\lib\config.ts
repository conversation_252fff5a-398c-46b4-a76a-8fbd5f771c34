// Configuration for the interview application

export interface AppConfig {
  did: {
    apiKey: string;
    sourceUrl?: string;
    voiceId?: string;
    provider?: string;
  };
  interview: {
    enableVideoGeneration: boolean;
    maxQuestions: number;
    defaultTimeout: number;
  };
}

// Default configuration
const defaultConfig: AppConfig = {
  did: {
    apiKey: process.env.NEXT_PUBLIC_DID_API_KEY || '',
    sourceUrl: process.env.NEXT_PUBLIC_DID_SOURCE_URL,
    voiceId: process.env.NEXT_PUBLIC_DID_VOICE_ID,
    provider: process.env.NEXT_PUBLIC_DID_PROVIDER || 'microsoft',
  },
  interview: {
    enableVideoGeneration: process.env.NEXT_PUBLIC_ENABLE_VIDEO_GENERATION === 'true',
    maxQuestions: parseInt(process.env.NEXT_PUBLIC_MAX_QUESTIONS || '10'),
    defaultTimeout: parseInt(process.env.NEXT_PUBLIC_DEFAULT_TIMEOUT || '30000'),
  },
};

// Get configuration with validation
export function getConfig(): AppConfig {
  const config = { ...defaultConfig };
  
  // Validate required fields
  if (config.interview.enableVideoGeneration && !config.did.apiKey) {
    console.warn('D-ID API key not found. Video generation will be disabled.');
    config.interview.enableVideoGeneration = false;
  }
  
  return config;
}

// Get D-ID configuration
export function getDIDConfig() {
  const config = getConfig();
  
  if (!config.interview.enableVideoGeneration || !config.did.apiKey) {
    return null;
  }
  
  return {
    apiKey: config.did.apiKey,
    sourceUrl: config.did.sourceUrl,
    voiceId: config.did.voiceId,
    provider: config.did.provider,
  };
}

// Check if video generation is enabled
export function isVideoGenerationEnabled(): boolean {
  const config = getConfig();
  return config.interview.enableVideoGeneration && !!config.did.apiKey;
}
