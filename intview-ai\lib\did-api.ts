// D-ID API Integration Service
// Based on patterns from AI speaking bot implementation

export interface DIDTalkRequest {
  script: {
    type: 'text';
    input: string;
    voice_id?: string;
    provider?: string;
    style?: string;
  };
  source_url?: string;
  config?: {
    fluent?: boolean;
    pad_audio?: number;
    driver_expressions?: {
      expressions?: Array<{
        start_frame: number;
        expression: string;
        intensity: number;
      }>;
    };
  };
}

export interface DIDTalkResponse {
  id: string;
  status: 'created' | 'started' | 'done' | 'error';
  result_url?: string;
  error?: {
    kind: string;
    description: string;
  };
  metadata?: {
    driver_url?: string;
    mouth_open?: string;
    source_url?: string;
  };
}

export interface DIDApiError {
  message: string;
  status?: number;
  details?: string;
}

class DIDApiService {
  private readonly apiUrl = 'https://api.d-id.com/talks';
  private readonly apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * Create a new talk (video generation) request
   */
  async createTalk(request: DIDTalkRequest): Promise<DIDTalkResponse> {
    try {
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${btoa(this.apiKey)}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create talk: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data: DIDTalkResponse = await response.json();
      return data;
    } catch (error) {
      console.error('Error creating D-ID talk:', error);
      throw new DIDApiError({
        message: error instanceof Error ? error.message : 'Unknown error creating talk',
        status: error instanceof Error && 'status' in error ? (error as any).status : undefined,
      });
    }
  }

  /**
   * Get the status of a talk by ID
   */
  async getTalkStatus(talkId: string): Promise<DIDTalkResponse> {
    try {
      const response = await fetch(`${this.apiUrl}/${talkId}`, {
        headers: {
          'Authorization': `Basic ${btoa(this.apiKey)}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get talk status: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data: DIDTalkResponse = await response.json();
      return data;
    } catch (error) {
      console.error('Error getting D-ID talk status:', error);
      throw new DIDApiError({
        message: error instanceof Error ? error.message : 'Unknown error getting talk status',
        status: error instanceof Error && 'status' in error ? (error as any).status : undefined,
      });
    }
  }

  /**
   * Poll for talk completion with timeout
   */
  async waitForTalkCompletion(
    talkId: string,
    options: {
      pollInterval?: number;
      maxAttempts?: number;
    } = {}
  ): Promise<DIDTalkResponse> {
    const { pollInterval = 3000, maxAttempts = 20 } = options;
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const status = await this.getTalkStatus(talkId);
        
        if (status.status === 'done' && status.result_url) {
          return status;
        }
        
        if (status.status === 'error') {
          throw new DIDApiError({
            message: `Talk generation failed: ${status.error?.description || 'Unknown error'}`,
            details: status.error?.kind,
          });
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval));
        attempts++;
      } catch (error) {
        if (error instanceof DIDApiError) {
          throw error;
        }
        console.error('Error polling talk status:', error);
        attempts++;
        if (attempts >= maxAttempts) {
          throw new DIDApiError({
            message: 'Timeout waiting for talk completion',
            details: `Failed after ${maxAttempts} attempts`,
          });
        }
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    }

    throw new DIDApiError({
      message: 'Timeout waiting for talk completion',
      details: `No response after ${maxAttempts} attempts`,
    });
  }

  /**
   * Generate a complete video with question text
   */
  async generateQuestionVideo(
    questionText: string,
    options: {
      sourceUrl?: string;
      voiceId?: string;
      provider?: string;
    } = {}
  ): Promise<string> {
    try {
      // Create the talk request
      const talkRequest: DIDTalkRequest = {
        script: {
          type: 'text',
          input: questionText,
          voice_id: options.voiceId,
          provider: options.provider,
        },
        source_url: options.sourceUrl,
        config: {
          fluent: true,
          pad_audio: 0.5,
        },
      };

      // Create the talk
      const createResponse = await this.createTalk(talkRequest);
      
      // Wait for completion
      const completedTalk = await this.waitForTalkCompletion(createResponse.id);
      
      if (!completedTalk.result_url) {
        throw new DIDApiError({
          message: 'No video URL returned from completed talk',
        });
      }

      return completedTalk.result_url;
    } catch (error) {
      console.error('Error generating question video:', error);
      throw error instanceof DIDApiError ? error : new DIDApiError({
        message: error instanceof Error ? error.message : 'Unknown error generating video',
      });
    }
  }
}

// Export a factory function to create the service
export function createDIDApiService(apiKey: string): DIDApiService {
  return new DIDApiService(apiKey);
}

// Export the service class for direct instantiation if needed
export { DIDApiService };
