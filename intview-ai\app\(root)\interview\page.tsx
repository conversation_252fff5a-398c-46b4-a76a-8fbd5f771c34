"use client";
import React, { useState } from "react";
import InterviewInstructions from "@/components/interview/InterviewInstructions";
import QuestionsPage from "@/components/interview/QuestionsPage";
import SequentialQuestionsPage from "@/components/interview/SequentialQuestionsPage";
import InterviewRecording from "@/components/interview/InterviewRecording";
import FinishInterview from "@/components/interview/FinishInterview";
import Analysis from "@/components/interview/Analysis";
import { getDIDConfig } from "@/lib/config";

type InterviewStep =
  | "instructions"
  | "questions"
  | "sequentialInterview"
  | "recording"
  | "finishInterview"
  | "analysis";

const Interview = () => {
  const [currentStep, setCurrentStep] = useState<InterviewStep>("instructions");
  const didConfig = getDIDConfig();

  const renderCurrentComponent = () => {
    switch (currentStep) {
      case "instructions":
        return (
          <InterviewInstructions onNext={() => setCurrentStep("questions")} />
        );
      case "questions":
        return <QuestionsPage onNext={() => setCurrentStep("sequentialInterview")} />;
      case "sequentialInterview":
        return (
          <SequentialQuestionsPage
            onNext={() => setCurrentStep("finishInterview")}
            didConfig={didConfig || undefined}
          />
        );
      case "recording":
        return (
          <InterviewRecording
            onNext={() => setCurrentStep("finishInterview")}
          />
        );
      case "finishInterview":
        return <FinishInterview onNext={() => setCurrentStep("analysis")} />;

      case "analysis":
        return <Analysis />;
      default:
        return (
          <InterviewInstructions onNext={() => setCurrentStep("questions")} />
        );
    }
  };

  return <div>{renderCurrentComponent()}</div>;
};

export default Interview;
