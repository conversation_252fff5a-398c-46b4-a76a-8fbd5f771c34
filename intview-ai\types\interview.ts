// Types for Sequential Interview System

export interface InterviewQuestion {
  id: string;
  text: string;
  order: number;
  category?: string;
  expectedDuration?: number; // in seconds
}

export interface QuestionResponse {
  questionId: string;
  userAnswer: string;
  timestamp: Date;
  duration?: number; // time taken to answer in seconds
  videoUrl?: string; // D-ID generated video URL for the question
}

export interface InterviewSession {
  id: string;
  startTime: Date;
  endTime?: Date;
  currentQuestionIndex: number;
  questions: InterviewQuestion[];
  responses: QuestionResponse[];
  status: 'not_started' | 'in_progress' | 'completed' | 'paused';
  candidateId?: string;
  jobId?: string;
}

export interface SequentialInterviewState {
  session: InterviewSession | null;
  currentQuestion: InterviewQuestion | null;
  currentVideoUrl: string | null;
  isGeneratingVideo: boolean;
  isSubmitting: boolean;
  userAnswer: string;
  error: string | null;
}

export interface SequentialInterviewActions {
  startInterview: (questions?: InterviewQuestion[]) => void;
  submitAnswer: (answer: string) => Promise<void>;
  nextQuestion: () => Promise<void>;
  pauseInterview: () => void;
  resumeInterview: () => void;
  completeInterview: () => void;
  resetInterview: () => void;
  setUserAnswer: (answer: string) => void;
  clearError: () => void;
}

// Default interview questions
export const DEFAULT_INTERVIEW_QUESTIONS: InterviewQuestion[] = [
  {
    id: 'q1',
    text: 'Tell us about yourself and your background.',
    order: 1,
    category: 'introduction',
    expectedDuration: 120,
  },
  {
    id: 'q2',
    text: 'What are your key strengths and how do they relate to this position?',
    order: 2,
    category: 'strengths',
    expectedDuration: 90,
  },
  {
    id: 'q3',
    text: 'Why do you want to work for our company?',
    order: 3,
    category: 'motivation',
    expectedDuration: 90,
  },
  {
    id: 'q4',
    text: 'Where do you see yourself in 5 years?',
    order: 4,
    category: 'career_goals',
    expectedDuration: 90,
  },
];

// Configuration for D-ID integration
export interface DIDConfig {
  apiKey: string;
  sourceUrl?: string;
  voiceId?: string;
  provider?: string;
}

// Error types
export class InterviewError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'InterviewError';
  }
}

export class DIDIntegrationError extends InterviewError {
  constructor(message: string, details?: any) {
    super(message, 'DID_INTEGRATION_ERROR', details);
    this.name = 'DIDIntegrationError';
  }
}
