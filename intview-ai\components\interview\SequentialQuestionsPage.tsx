'use client';

import React, { useEffect, useState } from 'react';
import { <PERSON>R<PERSON>, Loader2, AlertCircle } from 'lucide-react';
import JobInfoCard from '@/components/JobInfoCard';
import QuestionsList from '@/components/QuestionsList';
import CandidateImage from '@/components/CandidateImage';
import InterviewLayout from '@/components/InterviewLayout';
import { Button } from '@/components/ui/button';
import { useSequentialInterview } from '@/hooks/useSequentialInterview';
import { DIDConfig } from '@/types/interview';

interface SequentialQuestionsPageProps {
  onNext?: () => void;
  didConfig?: DIDConfig;
}

const SequentialQuestionsPage: React.FC<SequentialQuestionsPageProps> = ({
  onNext,
  didConfig,
}) => {
  const [userAnswer, setUserAnswer] = useState('');
  
  const {
    session,
    currentQuestion,
    currentVideoUrl,
    isGeneratingVideo,
    isSubmitting,
    error,
    isInterviewActive,
    isInterviewCompleted,
    currentQuestionNumber,
    totalQuestions,
    canSubmit,
    hasError,
    startInterview,
    submitAnswer,
    clearError,
  } = useSequentialInterview({
    didConfig,
    autoGenerateVideos: !!didConfig,
  });

  // Start interview on component mount
  useEffect(() => {
    if (!session) {
      startInterview();
    }
  }, [session, startInterview]);

  // Handle interview completion
  useEffect(() => {
    if (isInterviewCompleted && onNext) {
      onNext();
    }
  }, [isInterviewCompleted, onNext]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (canSubmit && userAnswer.trim()) {
      await submitAnswer(userAnswer.trim());
      setUserAnswer('');
    }
  };

  // Handle textarea change
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setUserAnswer(e.target.value);
  };

  // Handle key down for textarea
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey && canSubmit) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };

  if (!session) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Initializing interview...</span>
        </div>
      </div>
    );
  }

  if (isInterviewCompleted) {
    return (
      <div className="h-screen">
        <JobInfoCard />
        <InterviewLayout>
          <div className="flex flex-col items-center justify-center space-y-6">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-green-600 mb-4">
                Interview Completed!
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                Thank you for completing the interview. Your responses have been recorded.
              </p>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <p className="text-green-800">
                  You answered {session.responses.length} out of {totalQuestions} questions.
                </p>
              </div>
              <Button
                variant="default"
                size="lg"
                className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white"
                onClick={() => onNext && onNext()}
              >
                Continue to Analysis
                <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
              </Button>
            </div>
          </div>
        </InterviewLayout>
      </div>
    );
  }

  return (
    <div className="h-screen">
      <JobInfoCard />

      <InterviewLayout>
        {/* Progress indicator */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-600">
              Question {currentQuestionNumber} of {totalQuestions}
            </span>
            <span className="text-sm font-medium text-gray-600">
              {Math.round((currentQuestionNumber / totalQuestions) * 100)}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-[#6938EF] h-2 rounded-full transition-all duration-500"
              style={{ width: `${(currentQuestionNumber / totalQuestions) * 100}%` }}
            />
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start">
          {/* Questions List - Updated to show current question */}
          <QuestionsList 
            className="h-[550px]" 
            currentQuestion={currentQuestionNumber}
          />
          
          {/* D-ID Avatar in place of CandidateImage */}
          <CandidateImage 
            showDIDAvatar={true}
            videoUrl={currentVideoUrl}
            isGeneratingVideo={isGeneratingVideo}
          />
        </div>

        {/* Current Question Display */}
        {currentQuestion && (
          <div className="mt-6 bg-white rounded-2xl p-6 shadow-sm border max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold text-[#6938EF] mb-4">
              Current Question
            </h3>
            <p className="text-gray-800 text-lg leading-relaxed mb-6">
              {currentQuestion.text}
            </p>

            {/* Answer Input */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="answer" className="block text-sm font-medium text-gray-700 mb-2">
                  Your Answer
                </label>
                <textarea
                  id="answer"
                  value={userAnswer}
                  onChange={handleTextareaChange}
                  onKeyDown={handleKeyDown}
                  placeholder="Type your answer here..."
                  className="w-full border-2 border-gray-200 rounded-xl px-4 py-3 resize-none focus:border-[#6938EF] focus:ring-2 focus:ring-[#6938EF]/20 transition-all duration-200 placeholder-gray-400"
                  rows={4}
                  disabled={isSubmitting || isGeneratingVideo}
                />
              </div>

              {/* Error Display */}
              {hasError && (
                <div className="flex items-center space-x-2 text-red-600 bg-red-50 border border-red-200 rounded-lg p-3">
                  <AlertCircle className="w-4 h-4 flex-shrink-0" />
                  <span className="text-sm">{error}</span>
                  <button
                    type="button"
                    onClick={clearError}
                    className="ml-auto text-red-400 hover:text-red-600"
                  >
                    ×
                  </button>
                </div>
              )}

              {/* Submit Button */}
              <div className="flex justify-center">
                <Button
                  type="submit"
                  disabled={!canSubmit}
                  variant="default"
                  size="lg"
                  className="py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-6 h-6 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      Submit Answer
                      <ArrowRight className="w-6 h-6 duration-300 group-hover:translate-x-1" />
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>
        )}

        {/* Loading state for video generation */}
        {isGeneratingVideo && (
          <div className="mt-4 text-center">
            <p className="text-gray-600 text-sm">
              AI is preparing the next question...
            </p>
          </div>
        )}
      </InterviewLayout>
    </div>
  );
};

export default SequentialQuestionsPage;
