'use client';

import React from 'react';
import Image from "next/image";
import { Loader2, Bot } from 'lucide-react';
import AvatarImage from "@/public/images/avator.png";

type CandidateImageProps = {
  className?: string;
  videoUrl?: string | null;
  isGeneratingVideo?: boolean;
  showDIDAvatar?: boolean;
};

const CandidateImage = ({
  className,
  videoUrl,
  isGeneratingVideo = false,
  showDIDAvatar = false
}: CandidateImageProps) => {
  // If D-ID avatar should be shown
  if (showDIDAvatar) {
    return (
      <div className={`mt-6 md:mt-0 ${className}`}>
        <div className="w-[300px] h-[300px] rounded-lg overflow-hidden bg-gradient-to-br from-indigo-50 to-purple-50 border border-gray-200 flex items-center justify-center relative">
          {isGeneratingVideo ? (
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="w-12 h-12 animate-spin text-[#6938EF]" />
              <p className="text-gray-600 text-center text-sm">
                AI is preparing to ask the question...
              </p>
            </div>
          ) : videoUrl ? (
            <video
              src={videoUrl}
              controls
              autoPlay
              className="w-full h-full object-cover"
              onError={() => console.error('Error loading D-ID video')}
            />
          ) : (
            <div className="flex flex-col items-center space-y-4">
              <div className="w-20 h-20 bg-gradient-to-br from-[#6938EF] to-[#8b5cf6] rounded-full flex items-center justify-center">
                <Bot className="w-10 h-10 text-white" />
              </div>
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-800 mb-1">
                  AI Interviewer
                </h3>
                <p className="text-gray-600 text-sm">
                  Ready to ask questions
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Default static image
  return (
    <div className="mt-6 md:mt-0">
      <Image
        src={AvatarImage}
        alt="Interviewee"
        className={`rounded-lg object-cover ${className}`}
        width={300}
        height={300}
      />
    </div>
  );
};

export default CandidateImage;
