This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

# Interview AI - Sequential Question System

An AI-powered interview platform with sequential question-asking functionality using D-ID avatars.

## Features

- **Sequential Interview Flow**: Questions are asked one by one with submit-to-proceed functionality
- **D-ID Avatar Integration**: AI avatar asks questions via generated videos
- **Progress Tracking**: Visual progress indicators and question numbering
- **Responsive Design**: Works on desktop and mobile devices
- **TypeScript Support**: Fully typed for better development experience

## Getting Started

### Prerequisites

1. **D-ID API Key**: Get your API key from [D-ID Studio](https://studio.d-id.com/)
2. **Node.js**: Version 18 or higher

### Environment Setup

1. Copy the example environment file:
```bash
cp .env.example .env.local
```

2. Add your D-ID API key to `.env.local`:
```env
NEXT_PUBLIC_DID_API_KEY=your_did_api_key_here
NEXT_PUBLIC_ENABLE_VIDEO_GENERATION=true
```

### Installation and Development

First, install dependencies:

```bash
npm install
```

Then, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## D-ID Integration

### Configuration Options

The following environment variables can be configured:

```env
# Required for video generation
NEXT_PUBLIC_DID_API_KEY=your_api_key

# Optional: Custom avatar image
NEXT_PUBLIC_DID_SOURCE_URL=https://example.com/avatar.jpg

# Optional: Voice settings
NEXT_PUBLIC_DID_VOICE_ID=en-US-JennyNeural
NEXT_PUBLIC_DID_PROVIDER=microsoft

# Feature toggles
NEXT_PUBLIC_ENABLE_VIDEO_GENERATION=true
NEXT_PUBLIC_MAX_QUESTIONS=10
```

### How It Works

1. **Question Display**: Each question is shown one at a time
2. **Video Generation**: D-ID API generates avatar videos for each question
3. **User Interaction**: Users must submit their answer to proceed
4. **Progress Tracking**: Visual indicators show interview progress
5. **Completion**: Interview ends when all questions are answered

### Fallback Mode

If D-ID API key is not provided or video generation fails, the system falls back to text-based questions without video avatars.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
